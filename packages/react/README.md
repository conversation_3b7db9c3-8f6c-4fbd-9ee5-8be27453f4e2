# Bubl React

A React component for embedding the Bubl chat widget in React applications.

> **Note**: This package now defaults to Widget V2 with Shadow DOM isolation and improved performance. For the legacy v1 widget, use `BublChat`. For the new v2 widget, use `BublChatV2`.

## Installation

```bash
# npm
npm install @bubl/react

# yarn
yarn add @bubl/react

# pnpm
pnpm add @bubl/react
```

## Usage

### Widget V2 (Recommended)

```tsx
import { BublChatV2 } from '@bubl/react';

function App() {
  return (
    <div>
      {/* Your website content */}

      <BublChatV2
        websiteId="your-website-id"
        primaryColor="#4F46E5"
        secondaryColor="#FFFFFF"
        position="bottom-right"
        welcomeMessage="Hi there! How can I help you today?"
        headerText="Chat Assistant"
        initiallyOpen={false}
        onReady={() => console.log("Bubl widget v2 is ready")}
      />
    </div>
  );
}
```

### Widget V1 (Legacy - now uses V2 loader)

```tsx
import { BublChat } from '@bubl/react';

function App() {
  return (
    <div>
      {/* Your website content */}

      <BublChat
        websiteId="your-website-id"
        primaryColor="#4F46E5"
        secondaryColor="#FFFFFF"
        position="bottom-right"
        welcomeMessage="Hi there! How can I help you today?"
        headerText="Chat Assistant"
        initiallyOpen={false}
        onReady={() => console.log("Bubl widget is ready")}
      />
    </div>
  );
}
```

## Props

| Prop | Type | Default | Description |
| --- | --- | --- | --- |
| `websiteId` | `string` | **Required** | Your website ID from the Bubl dashboard |
| `primaryColor` | `string` | `"#4F46E5"` | Primary color for the chat widget (hex code) |
| `secondaryColor` | `string` | `"#FFFFFF"` | Secondary color for the chat widget (hex code) |
| `position` | `"bottom-right"` \| `"bottom-left"` | `"bottom-right"` | Position of the chat widget |
| `welcomeMessage` | `string` | `"Hi there! How can I help you today?"` | Welcome message displayed when the chat is opened |
| `headerText` | `string` | `"Chat Assistant"` | Text displayed in the header of the chat widget |
| `initiallyOpen` | `boolean` | `false` | Whether the chat widget should be initially open |
| `onReady` | `() => void` | `undefined` | Callback function called when the widget is ready |

## Controlling the Widget

You can control the widget programmatically using the global `Bubl.api` object:

```tsx
// Open the chat widget
window.Bubl?.api?.open();

// Close the chat widget
window.Bubl?.api?.close();

// Toggle the chat widget
window.Bubl?.api?.toggle();

// Maximize the chat widget to fullscreen
window.Bubl?.api?.maximize();

// Restore the chat widget to normal size
window.Bubl?.api?.restore();

// Toggle between maximized and normal size
window.Bubl?.api?.toggleMaximize();
```

## Custom App URL

If you're self-hosting Bubl, you can specify a custom app URL by setting the `window.bublConfig.appUrl` property before rendering the component:

```tsx
// Set custom app URL
window.bublConfig = {
  appUrl: "https://your-bubl-instance.com"
};

// Then render the component
ReactDOM.render(
  <BublChat websiteId="your-website-id" />,
  document.getElementById('root')
);
```

## Styling

The Bubl chat widget is designed to be completely non-intrusive and will not affect the styles of your website.

### Widget V2 (BublChatV2)
- **Complete isolation**: Uses Shadow DOM for perfect style isolation
- **Zero interference**: Cannot affect or be affected by your website's styles
- **No global styles**: All styles are contained within the Shadow DOM
- **Better performance**: Optimized CSS-in-JS implementation

### Widget V1 (BublChat - now uses V2 loader)
- **Improved isolation**: Now uses Widget V2 loader with Shadow DOM
- **Non-intrusive**: Will not affect your website's styles
- **Transparent background**: Clean integration with your design

## License

MIT
