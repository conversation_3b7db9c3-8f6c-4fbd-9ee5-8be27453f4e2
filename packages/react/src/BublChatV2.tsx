import { useEffect, useRef } from "react"

export interface BublChatV2Props {
  /**
   * The website ID from your Bubl dashboard
   */
  websiteId: string

  /**
   * Primary color for the chat widget (hex color)
   * @default "#4F46E5"
   */
  primaryColor?: string

  /**
   * Secondary color for the chat widget (hex color)
   * @default "#FFFFFF"
   */
  secondaryColor?: string

  /**
   * Position of the chat widget
   * @default "bottom-right"
   */
  position?: "bottom-right" | "bottom-left"

  /**
   * Welcome message shown when the chat is first opened
   * @default "Hi there! How can I help you today?"
   */
  welcomeMessage?: string

  /**
   * Text shown in the chat header
   * @default "Chat Assistant"
   */
  headerText?: string

  /**
   * Whether the chat should be initially open
   * @default false
   */
  initiallyOpen?: boolean

  /**
   * Callback function called when the widget is ready
   */
  onReady?: () => void

  /**
   * Custom API base URL (for self-hosted instances)
   */
  apiBaseUrl?: string
}

/**
 * BublChatV2 is the next-generation React component for embedding the Bubl chat widget.
 *
 * This version features:
 * - Complete style isolation using Shadow DOM
 * - Zero interference with host website functionality
 * - Improved performance and smaller bundle size
 * - Better cross-browser compatibility
 * - Enhanced security and stability
 *
 * @example
 * ```tsx
 * import { BublChatV2 } from "@bubl/react";
 *
 * function App() {
 *   return (
 *     <div>
 *       <h1>My Website</h1>
 *       <BublChatV2
 *         websiteId="your-website-id"
 *         primaryColor="#4F46E5"
 *         position="bottom-right"
 *       />
 *     </div>
 *   );
 * }
 * ```
 */
export function BublChatV2({
  websiteId,
  primaryColor = "#4F46E5",
  secondaryColor = "#FFFFFF",
  position = "bottom-right",
  welcomeMessage = "Hi there! How can I help you today?",
  headerText = "Chat Assistant",
  initiallyOpen = false,
  onReady,
  apiBaseUrl,
}: BublChatV2Props) {
  const scriptLoaded = useRef(false)
  const appUrl = apiBaseUrl ||
    (typeof window !== 'undefined'
      ? window.location.origin
      : "https://bublai.com")

  useEffect(() => {
    // Skip if the script is already loaded or if we're not in the browser
    if (scriptLoaded.current || typeof window === "undefined") {
      return
    }

    // Mark as loaded to prevent duplicate loading
    scriptLoaded.current = true

    // Set up the global Bubl object with configuration
    window.Bubl = window.Bubl || {
      config: {
        websiteId,
        primaryColor,
        secondaryColor,
        position,
        welcomeMessage,
        headerText,
        initiallyOpen,
        apiBaseUrl: appUrl,
      },
      onReady: onReady || function () {
        console.log("Bubl widget v2 is ready")
      },
    }

    // Create and load the v2 script
    const script = document.createElement("script")
    script.async = true
    script.src = `${appUrl}/widget/v2/loader.js`

    // Add error handling
    script.onerror = () => {
      console.error("Failed to load Bubl widget v2")
      scriptLoaded.current = false
    }

    // Append the script to the document
    const firstScript = document.getElementsByTagName("script")[0]
    firstScript.parentNode?.insertBefore(script, firstScript)

    // Clean up function
    return () => {
      // Use the v2 API to destroy the widget
      if (window.Bubl?.api?.destroy) {
        window.Bubl.api.destroy()
      }

      // Remove the script if it exists
      if (script.parentNode) {
        script.parentNode.removeChild(script)
      }

      // Reset the loaded flag
      scriptLoaded.current = false

      // Clean up global object
      delete window.Bubl
      delete window.__BUBL_WIDGET_LOADED__
    }
  }, [
    websiteId,
    primaryColor,
    secondaryColor,
    position,
    welcomeMessage,
    headerText,
    initiallyOpen,
    onReady,
    appUrl,
  ])

  // This component doesn't render anything visible
  return null
}

// Global type declarations are in global.d.ts
