/**
 * B<PERSON>l React Package Styles
 * These styles ensure proper transparency and isolation for the widget
 * IMPORTANT: All styles are scoped to the widget elements only
 */

/* Ensure the widget container is transparent and doesn't inherit color schemes */
#bubl-widget-container {
  background-color: transparent !important;
  color-scheme: none !important;
  transition: all 0.3s ease !important;
  pointer-events: none !important; /* Allow clicks to pass through to underlying elements */
}

/* Ensure the widget is transparent and properly isolated */
#bubl-widget {
  background-color: transparent !important;
  background: transparent !important;
  color-scheme: none !important;
  transition: all 0.3s ease !important;
  pointer-events: auto !important; /* Ensure widget receives pointer events */
}

/* Ensure the widget content is transparent */
#bubl-widget * {
  background-color: transparent !important;
  background: transparent !important;
  color-scheme: none !important;
}

/* Fullscreen mode for the widget */
#bubl-widget[data-fullscreen="true"] {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  max-height: 100vh !important;
  border-radius: 0 !important;
  z-index: 2147483647 !important; /* Maximum z-index value */
}
