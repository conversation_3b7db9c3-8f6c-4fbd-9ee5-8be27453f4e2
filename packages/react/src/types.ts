/**
 * Global type declarations for the Bubl chat widget
 */

interface BublConfig {
  websiteId: string;
  primaryColor?: string;
  secondaryColor?: string;
  position?: string;
  welcomeMessage?: string;
  headerText?: string;
  initiallyOpen?: boolean;
  apiBaseUrl?: string;
}

interface BublApi {
  open: () => void;
  close: () => void;
  toggle: () => void;
  maximize: () => void;
  restore: () => void;
  toggleMaximize: () => void;
  destroy?: () => void;
}

interface BublObject {
  config: BublConfig;
  onReady?: () => void;
  api?: BublApi;
  _internal?: Record<string, unknown>;
}

interface BublGlobalConfig {
  appUrl?: string;
}

declare global {
  interface Window {
    Bubl?: BublObject;
    bublConfig?: BublGlobalConfig;
    __BUBL_WIDGET_LOADED__?: boolean;
  }
}

export {};
