"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { useWebsites } from "@/hooks";
import { useEffect, useState } from "react";

// Type definitions are in global.d.ts

export default function TestChatPage() {
	const { data: websites, isLoading } = useWebsites();
	const [selectedWebsiteId, setSelectedWebsiteId] = useState<string | null>(
		null,
	);
	const [position, setPosition] = useState<"bottom-right" | "bottom-left">(
		"bottom-right",
	);
	const [showChat, setShowChat] = useState(false);
	const [widgetLoaded, setWidgetLoaded] = useState(false);

	const handleSelectWebsite = (websiteId: string) => {
		setSelectedWebsiteId(websiteId);
		setShowChat(false); // Reset chat when changing website
		// Destroy existing widget if any
		if (window.Bubl?.api?.destroy) {
			window.Bubl.api.destroy();
			setWidgetLoaded(false);
		}
	};

	const handleShowChat = () => {
		setShowChat(true);
		loadWidget();
	};

	const loadWidget = () => {
		if (widgetLoaded || !selectedWebsiteId) return;

		// Create script element
		const script = document.createElement("script");
		script.async = true;
		script.src = "/widget/v2/loader.js";

		// Set up configuration with better color contrast
		window.Bubl = {
			config: {
				websiteId: selectedWebsiteId,
				primaryColor: "#4F46E5", // Indigo for primary actions
				secondaryColor: "#F8FAFC", // Light gray for backgrounds
				position: position,
				welcomeMessage:
					"Hi there! I'm your AI assistant. How can I help you today?",
				headerText: "Website Assistant",
				initiallyOpen: true,
				apiBaseUrl: window.location.origin,
			},
			onReady: () => {
				setWidgetLoaded(true);
			},
		};

		// Add error handling
		script.onerror = () => {
			console.error("Failed to load Widget V2");
		};

		// Append to document
		document.head.appendChild(script);
	};

	// Cleanup on unmount
	useEffect(() => {
		return () => {
			if (window.Bubl?.api?.destroy) {
				window.Bubl.api.destroy();
			}
		};
	}, []);

	return (
		<div>
			<div className="p-6 space-y-6">
				<Card>
					<CardHeader>
						<CardTitle>Chat Widget Configuration</CardTitle>
						<CardDescription>
							Select a website and configure the chat widget
						</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="space-y-4">
							<div>
								<label
									htmlFor="website"
									className="block text-sm font-medium mb-2"
								>
									Select Website
								</label>
								<select
									className="w-full p-2 border rounded-md"
									value={selectedWebsiteId || ""}
									onChange={(e) => handleSelectWebsite(e.target.value)}
									disabled={isLoading}
								>
									<option value="">Select a website</option>
									{websites?.map((website) => (
										<option key={website.id} value={website.id}>
											{website.name}
										</option>
									))}
								</select>
							</div>

							<div>
								<label
									htmlFor="position"
									className="block text-sm font-medium mb-2"
								>
									Widget Position
								</label>
								<div className="flex space-x-4">
									<label className="flex items-center">
										<input
											type="radio"
											name="position"
											value="bottom-right"
											checked={position === "bottom-right"}
											onChange={() => setPosition("bottom-right")}
											className="mr-2"
										/>
										Bottom Right
									</label>
									<label className="flex items-center">
										<input
											type="radio"
											name="position"
											value="bottom-left"
											checked={position === "bottom-left"}
											onChange={() => setPosition("bottom-left")}
											className="mr-2"
										/>
										Bottom Left
									</label>
								</div>
							</div>

							<Button
								onClick={handleShowChat}
								disabled={!selectedWebsiteId || widgetLoaded}
								className="w-full"
							>
								{widgetLoaded ? "Widget V2 Loaded" : "Load Widget V2"}
							</Button>
						</div>
					</CardContent>
				</Card>

				{showChat && selectedWebsiteId && (
					<Card>
						<CardHeader>
							<CardTitle>Widget V2 Preview</CardTitle>
							<CardDescription>
								This is how your new Widget V2 will appear on your website
							</CardDescription>
						</CardHeader>
						<CardContent>
							<div className="space-y-4">
								<p className="text-sm text-muted-foreground">
									The Widget V2 will appear in the{" "}
									{position === "bottom-right" ? "bottom right" : "bottom left"}{" "}
									corner with complete style isolation using Shadow DOM. You can
									interact with it just like your website visitors would.
								</p>

								{widgetLoaded && (
									<div className="p-4 bg-green-50 border border-green-200 rounded-lg">
										<p className="text-sm text-green-800">
											✅ Widget V2 loaded successfully! The widget should now be
											visible in the{" "}
											{position === "bottom-right"
												? "bottom right"
												: "bottom left"}{" "}
											corner.
										</p>
									</div>
								)}

								<div className="text-sm text-muted-foreground space-y-2">
									<p>
										<strong>Widget V2 Features:</strong>
									</p>
									<ul className="list-disc list-inside space-y-1">
										<li>Complete style isolation with Shadow DOM</li>
										<li>Zero interference with page functionality</li>
										<li>Improved performance and smaller bundle size</li>
										<li>Enhanced security and stability</li>
										<li>Better cross-browser compatibility</li>
									</ul>
								</div>
							</div>
						</CardContent>
					</Card>
				)}
			</div>
		</div>
	);
}
