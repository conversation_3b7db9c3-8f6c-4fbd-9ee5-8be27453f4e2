/**
 * Bundle entry point for Widget V2
 * This file is the main entry point for the widget bundle
 */

import React from "react";
import { createRoot } from "react-dom/client";
import { initWidget } from "./WidgetV2";

// Expose the widget to the global scope
declare global {
	interface Window {
		BublWidgetV2: {
			init: typeof initWidget;
		};
	}
}

// Make the widget available globally
window.BublWidgetV2 = {
	init: initWidget,
};

// Export for TypeScript
export { initWidget };
